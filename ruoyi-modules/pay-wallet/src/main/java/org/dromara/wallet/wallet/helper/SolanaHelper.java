package org.dromara.wallet.wallet.helper;

import cn.hutool.core.thread.ThreadUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bitcoinj.core.Base58;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.wallet.config.SolanaCoinType;
import org.dromara.wallet.config.facade.SolanaConfigFacade;
import org.dromara.wallet.config.solana.SolanaRpcConfig;
import org.dromara.wallet.config.solana.SolanaWalletConfig;
import org.dromara.wallet.domain.bo.MetaSolanaCstaddressinfoBo;
import org.dromara.wallet.wallet.exception.BlockchainTransferException;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.BlockchainTransferSuccess;
import org.dromara.wallet.wallet.transfer.dto.TransactionConfirmationResult;
import org.dromara.wallet.wallet.transfer.service.TransactionConfirmationService;
import org.p2p.solanaj.core.Account;
import org.p2p.solanaj.core.PublicKey;
import org.p2p.solanaj.core.Transaction;
import org.p2p.solanaj.core.TransactionInstruction;
import org.p2p.solanaj.programs.AssociatedTokenProgram;
import org.p2p.solanaj.programs.ComputeBudgetProgram;
import org.p2p.solanaj.programs.SystemProgram;
import org.p2p.solanaj.programs.TokenProgram;
import org.p2p.solanaj.rpc.RpcClient;
import org.p2p.solanaj.rpc.RpcException;
import org.p2p.solanaj.rpc.types.*;
import org.p2p.solanaj.rpc.types.config.Commitment;
import org.p2p.solanaj.utils.TweetNaclFast;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @date 2025/6/27 14:47
 **/
@Slf4j
@Component
@SuppressWarnings("unused")
@RequiredArgsConstructor
public class SolanaHelper implements TransactionConfirmationService {
    private static final String JSON_RPC = "2.0";
    private static final String ID = "1";

    private final SolanaRpcConfig solanaRpcConfig;
    private final SolanaWalletConfig solanaWalletConfig;
    private final SolanaConfigFacade solanaFacade;
    private final ApplicationContext applicationContext;

    /**
     * 获取当前类的Spring代理对象，用于@Async等AOP功能
     * 解决同一个类内部方法调用时AOP不生效的问题
     */
    private SolanaHelper getSelf() {
        return applicationContext.getBean(SolanaHelper.class);
    }

    /**
     * Solana交易参数Map常量
     */
    public static final HashMap<String, Object> TRANSACTION_PARAMS_MAP = new HashMap<>() {{
        put("encoding", "json");
        put("maxSupportedTransactionVersion", 0);
        put("transactionDetails", "full");
        put("rewards", false);
    }};


    /**
     * 获取交易信息
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public ConfirmedTransaction getTransaction(String signature) {
        try {
            ConfirmedTransaction transaction = solanaRpcConfig.getRpcClient().getApi().getTransaction(signature);
            if (transaction != null) {
                return transaction;
            } else {
                throw new BaseException("交易哈希错误:" + signature);
            }
        } catch (RpcException e) {
            log.error("获取交易失败:{}", e.getMessage());
            throw new BaseException("获取交易失败:" + e.getMessage());
        }
    }

    /**
     * 获取交易最终失败后的恢复方法
     */
    @Recover
    @SuppressWarnings("unused") // Spring Retry框架运行时调用
    public ConfirmedTransaction recoverGetTransaction(BaseException e, String signature) {
        log.error("获取交易{}在多次尝试后失败：{}", signature, e.getMessage());
        throw new BaseException("获取交易经多次重试后失败:" + e.getMessage());
    }

    public HttpHeaders getHttpHeaders() {
        HttpHeaders standardHeaders = new HttpHeaders();
        standardHeaders.setContentType(MediaType.APPLICATION_JSON);
        standardHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        return standardHeaders;
    }

    /**
     * 获取最新的slot
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public long getSlot() {
        RpcClient client = solanaRpcConfig.getRpcClient();
        try {
            return client.getApi().getSlot();
        } catch (RpcException e) {
            log.error("获取最新slot失败", e);
            throw new BaseException("获取slot失败:" + e.getMessage());
        }
    }

    /**
     * 获取这段区间的可用slot
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public List<Long> getAvailableSlot(long beginSlot, long endSlot) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        try {
            return client.getApi().getBlocks(beginSlot, endSlot);
        } catch (RpcException e) {
            log.error("获取区间可用slot失败, beginSlot: {}, endSlot: {}", beginSlot, endSlot, e);
            throw new BaseException("获取可用slot失败:" + e.getMessage());
        }
    }


    /**
     * 获取指定slot数据
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public Block getSlotContent(long slot) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        try {
            return client.getApi().getBlock((int) slot, TRANSACTION_PARAMS_MAP);
        } catch (RpcException e) {
            log.error("获取指定slot数据失败, slot: {}", slot, e);
            throw new BaseException("获取指定slot数据失败:" + e.getMessage());
        }
    }

    /**
     * 获取账户金额
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public BigInteger getBalance(String address) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        PublicKey sourcePublicKey = new PublicKey(address);
        try {
            long balance = client.getApi().getBalance(sourcePublicKey);
            return BigInteger.valueOf(balance);
        } catch (RpcException e) {
            log.error("Solana余额查询失败: address={}, error={}", address, e.getMessage());
            throw new BaseException("获取账户余额失败:" + e.getMessage());
        }
    }

    /**
     * 获取账户Token金额
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     * 如果钱包没有该代币账户，返回null而不是抛出异常
     */
    @Retryable(retryFor = {BaseException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public TokenResultObjects.TokenAmountInfo getTokenBalance(String address, String tokenMintAddress) {
        PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
        RpcClient client = solanaRpcConfig.getRpcClient();

        PublicKey sourcePublicKey = new PublicKey(address);
        try {
            PublicKey sourceTokenPublicKey = client.getApi().getTokenAccountsByOwner(sourcePublicKey, tokenMintPublicKey);
            return client.getApi().getTokenAccountBalance(sourceTokenPublicKey);
        } catch (RpcException e) {
            // 检查是否是因为没有代币账户导致的错误
            if (isTokenAccountNotFoundError(e)) {
                log.debug("Solana代币余额查询: address={}, token={}, balance=0 (无代币账户)",
                    address, tokenMintAddress);
                return createZeroTokenBalance();
            }
            log.error("Solana代币余额查询失败: address={}, token={}, error={}",
                address, tokenMintAddress, e.getMessage());
            throw new BaseException("获取账户Token余额失败:" + e.getMessage());
        }
    }

    /**
     * 检查是否是代币账户不存在的错误
     */
    private boolean isTokenAccountNotFoundError(RpcException e) {
        String message = e.getMessage();
        return message != null && (
            message.contains("unable to get token account by owner") ||
                message.contains("No token accounts found") ||
                message.contains("Invalid param: could not find account") ||
                message.contains("TokenAccountNotFound") ||
                message.contains("Account not found")
        );
    }

    /**
     * 创建零余额的TokenAmountInfo
     */
    private TokenResultObjects.TokenAmountInfo createZeroTokenBalance() {
        // 由于TokenAmountInfo可能没有setter方法，我们返回null
        // 在调用处会检查null并处理为零余额
        return null;
    }

    // ==================== 统一余额查询方法 ====================

    /**
     * 获取SOL原生代币余额（可读格式）- 内部方法
     *
     * @param address 钱包地址
     * @return SOL余额（以SOL为单位，保留6位小数）
     */
    private BigDecimal balanceGetNativeForRead(String address) {
        BigInteger balance = getBalance(address);
        // SOL精度是9位小数（1 SOL = 1,000,000,000 lamports）
        return new BigDecimal(balance).divide(new BigDecimal("**********"), 6, RoundingMode.DOWN);
    }

    /**
     * 获取SPL代币余额（可读格式）- 内部方法
     *
     * @param address     钱包地址
     * @param tokenSymbol 代币符号（用于获取合约地址和精度）
     * @return 可读格式的余额（保留6位小数）
     * @throws BaseException 当不支持指定代币时抛出异常
     */
    private BigDecimal balanceGetTokenForRead(String address, String tokenSymbol) {
        // 通过代币符号获取合约地址
        String contractAddress = solanaFacade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new BaseException("Solana链不支持代币: " + tokenSymbol);
        }

        TokenResultObjects.TokenAmountInfo tokenBalance = getTokenBalance(address, contractAddress);
        if (tokenBalance == null) {
            return BigDecimal.ZERO;
        }

        // 获取代币精度
        int decimals = solanaFacade.getContractDecimals(tokenSymbol);
        BigDecimal divisor = new BigDecimal(10).pow(decimals);

        // 从TokenAmountInfo中获取余额
        String amountString = tokenBalance.getUiAmountString();
        if (amountString == null || amountString.trim().isEmpty() || "0".equals(amountString)) {
            return BigDecimal.ZERO;
        }

        try {
            return new BigDecimal(amountString).setScale(6, RoundingMode.DOWN);
        } catch (NumberFormatException e) {
            log.warn("解析Solana代币余额失败: address={}, token={}, amount={}",
                address, tokenSymbol, amountString);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 统一的代币余额查询方法（可读格式）- 唯一公共入口
     * 自动区分原生代币和SPL代币，这是余额查询的统一入口方法
     * 所有余额查询都应该通过此方法进行，其他方法为内部实现
     *
     * @param address     钱包地址
     * @param tokenSymbol 代币符号
     * @return 可读格式的余额（保留6位小数）
     */
    public BigDecimal balanceGetForRead(String address, String tokenSymbol) {
        try {
            BigDecimal balance;

            if ("SOL".equalsIgnoreCase(tokenSymbol)) {
                // SOL原生代币 - 直接调用原生代币查询方法
                balance = balanceGetNativeForRead(address);
            } else {
                // SPL代币 - 调用SPL代币查询方法
                balance = balanceGetTokenForRead(address, tokenSymbol);
            }

            // 统一的成功日志（避免重复记录）
            if (balance.compareTo(BigDecimal.ZERO) > 0) {
                log.info("Solana链余额查询: address={}, token={}, balance={}",
                    address, tokenSymbol, balance);
            }

            return balance;
        } catch (Exception e) {
            log.error("Solana链余额查询失败: address={}, token={}, error={}",
                address, tokenSymbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 验证交易签名
     * <p>
     * 添加重试机制，验证操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public boolean verify(String signature) {
        RpcClient client = solanaRpcConfig.getRpcClient();

        try {
            SignatureStatuses signatureStatuses = client.getApi().getSignatureStatuses(Collections.singletonList(signature), true);
            if (signatureStatuses.getValue().get(0).getConfirmations() == null) {
                return true;
            } else {
                ThreadUtil.sleep(2000);
                signatureStatuses = client.getApi().getSignatureStatuses(Collections.singletonList(signature), true);
                return signatureStatuses.getValue().get(0).getConfirmations() == null;
            }
        } catch (RpcException e) {
            log.error("验证交易签名失败, 签名: {}", signature, e);
            throw new BaseException("验证交易签名失败:" + e.getMessage());
        }
    }

    /**
     * 发起SOL原生代币交易（基础方法，供Strategy调用）
     * <p>
     * 注意：转账操作不应该简单重试，因为可能导致重复转账。
     * 交易可能已经成功但返回过程中出现问题，简单重试会导致资金多次转移。
     * <p>
     * 此方法供SolanaTransferStrategy调用，消除代码重复
     */
    public String transferNative(String sourcePrivateKey, String toAddress, long lamports) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        // 解码私钥（支持Base64和Base58格式）
        byte[] decode = decodePrivateKey(sourcePrivateKey);
        Account signer = new Account(decode);
        //目标账户
        PublicKey toPublicKey = new PublicKey(toAddress);

        //转账对象
        Transaction transaction = new Transaction();

        // 如果指定了优先级费用，添加ComputeBudgetProgram指令
        transaction.addInstruction(ComputeBudgetProgram.setComputeUnitPrice(500000));

        transaction.addInstruction(SystemProgram.transfer(signer.getPublicKey(), toPublicKey, lamports));

        try {
            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("Solana转账失败: from={}, to={}, amount={} lamports, error={}",
                signer.getPublicKey(), toAddress, lamports, e.getMessage());
            throw new BaseException("发起SOL交易失败:" + e.getMessage());
        }
    }

    /**
     * 解析账户地址
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public String parseAccountToSolAddress(String splTokenAddress) {
        PublicKey splTokenPublicKey = new PublicKey(splTokenAddress);
        RpcClient client = solanaRpcConfig.getRpcClient();

        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("encoding", "base64");
        AccountInfo accountInfo;
        try {
            accountInfo = client.getApi().getAccountInfo(splTokenPublicKey, paramsMap);
        } catch (RpcException e) {
            log.error("获取SPL Token账户信息失败, 地址: {}, 错误: {}", splTokenAddress, e.getMessage());
            throw new BaseException("获取SPL Token账户信息失败:" + e.getMessage());
        }
        //解码
        byte[] data = Base64.getDecoder().decode(accountInfo.getValue().getData().get(0));
        // 3. 取32~63字节（owner）
        byte[] owner = Arrays.copyOfRange(data, 32, 64);
        // 4. base58编码，得到SOL主账户地址
        return Base58.encode(owner);
    }

    /**
     * 获取指定地址的最近几次交易记录
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public List<SignatureInformation> getSignaturesForAddress(String address, int limit) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        try {
            List<SignatureInformation> signaturesForAddress = client.getApi().getSignaturesForAddress(new PublicKey(address), limit, Commitment.FINALIZED);
            signaturesForAddress.removeIf(item -> item != null && item.getErr() != null);
            return signaturesForAddress;
        } catch (RpcException e) {
            log.error("获取地址交易记录失败, 地址: {}, 限制: {}", address, limit, e);
            throw new BaseException("获取地址交易记录失败:" + e.getMessage());
        }
    }

    /**
     * 生成未使用的Solana账户（带验证逻辑）
     * 最多尝试3次生成，确保钱包地址未被使用
     *
     * @param bo 账户信息对象
     * @return 是否成功生成未使用的账户
     */
    public boolean generateUnusedAccount(MetaSolanaCstaddressinfoBo bo) {
        // 尝试创建钱包，最多尝试3次
        for (int attempts = 0; attempts < 3; attempts++) {
            // 生成新钱包
            generateAccount(bo);

            // 检查钱包是否已被使用
            if (!hasTransactionHistory(bo.getCstAddress())) {
                // 钱包未被使用，返回成功
                log.info("成功为用户{}生成未使用的钱包地址: {}", bo.getCstId(), bo.getCstAddress());
                return true;
            }

            log.warn("生成的钱包地址{}已被使用，正在重新生成... (尝试 {}/3)", bo.getCstAddress(), attempts + 1);
        }

        // 如果多次尝试后仍无法创建未使用的钱包，返回失败
        log.error("无法为用户{}生成未使用的钱包，已尝试3次", bo.getCstId());
        return false;
    }

    /**
     * 生成Solana账户（基础方法）
     *
     * @param bo 账户信息对象
     */
    public void generateAccount(MetaSolanaCstaddressinfoBo bo) {
        try {
            // 生成新的 Ed25519 密钥对
            TweetNaclFast.Signature.KeyPair keyPair = TweetNaclFast.Signature.keyPair();
            // 获取私钥和公钥
            byte[] secretKey = keyPair.getSecretKey(); // 64字节
            byte[] publicKey = keyPair.getPublicKey(); // 32字节
            // Solana 钱包地址就是公钥的 base58 编码
            String pubKeyBase58 = Base58.encode(publicKey);

            bo.setCstAddress(pubKeyBase58);
            bo.setCstPrivate(java.util.Base64.getEncoder().encodeToString(secretKey));

            // 2. 预计算ATA地址（不上链，可以立即保存到数据库）
            String usdtAta = calculateAssociatedTokenAddress(pubKeyBase58, SolanaCoinType.USDT.getContractAddress());
            String usdcAta = calculateAssociatedTokenAddress(pubKeyBase58, SolanaCoinType.USDC.getContractAddress());

            bo.setCstUsdtAddress(usdtAta);
            bo.setCstUsdcAddress(usdcAta);

            log.info("账户生成完成, 地址: {}, USDT ATA: {}, USDC ATA: {}",
                pubKeyBase58, usdtAta, usdcAta);

            // 3. 异步激活账号（在链上真正创建ATA账户）
            // 注意：必须通过Spring代理对象调用，否则@Async不会生效
            log.info("准备异步激活账户: {}, 当前线程: {}", bo.getCstAddress(), Thread.currentThread().getName());
            getSelf().activateAccountAsync(bo);
            log.info("异步激活任务已提交: {}, 当前线程: {}", bo.getCstAddress(), Thread.currentThread().getName());

        } catch (Exception e) {
            log.error("生成Solana账户失败", e);
            throw new BaseException("生成Solana账户失败:" + e.getMessage());
        }
    }

    /**
     * 检查钱包地址是否已被使用（是否有交易历史）
     *
     * @param walletAddress 钱包地址
     * @return 如果钱包有交易历史则返回true，否则返回false
     */
    public boolean hasTransactionHistory(String walletAddress) {
        try {
            // 获取钱包的最近1条交易签名信息
            List<SignatureInformation> signatures = getSignaturesForAddress(walletAddress, 1);

            // 如果有交易记录，则说明钱包已被使用
            return !signatures.isEmpty();
        } catch (Exception e) {
            log.error("检查钱包交易历史失败, 地址: {}", walletAddress, e);
            // 当查询异常时，保守起见，认为钱包可能已被使用
            return true;
        }
    }

    /**
     * 异步激活账户Token地址
     * 分别创建USDT和USDC地址，各自独立重试
     *
     * @param bo 账户信息对象
     */
    @Async
    public void activateAccountAsync(MetaSolanaCstaddressinfoBo bo) {
        log.info("开始异步激活账户: {}, 执行线程: {}", bo.getCstAddress(), Thread.currentThread().getName());

        // 分别创建USDT和USDC Token地址，各自独立重试
        createUsdtTokenAddressWithRetry(bo);
        createUsdcTokenAddressWithRetry(bo);

        log.info("账户激活完成: {}, 执行线程: {}", bo.getCstAddress(), Thread.currentThread().getName());
    }

    /**
     * 创建USDT Token地址（带重试）
     */
    @Retryable(
        retryFor = {Exception.class},
        backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public void createUsdtTokenAddressWithRetry(MetaSolanaCstaddressinfoBo bo) {
        try {
            if (bo.getCstUsdtAddress() == null) {
                log.error("USDT ATA地址未预计算，无法激活");
                throw new BaseException("USDT ATA地址未预计算");
            }

            // 激活USDT Token账户（在链上创建）
            activateTokenAccount(
                solanaWalletConfig.getFeeWalletPrivateKey(),
                bo.getCstAddress(),
                SolanaCoinType.USDT.getContractAddress(),
                bo.getCstUsdtAddress()
            );
            log.info("USDT Token账户激活成功: {}", bo.getCstUsdtAddress());

        } catch (Exception e) {
            log.error("激活USDT Token账户失败, 地址: {}, ATA: {}, 错误: {}",
                bo.getCstAddress(), bo.getCstUsdtAddress(), e.getMessage(), e);
            throw e; // 重新抛出异常以触发重试机制
        }
    }

    /**
     * 创建USDC Token地址（带重试）
     */
    @Retryable(
        retryFor = {Exception.class},
        backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public void createUsdcTokenAddressWithRetry(MetaSolanaCstaddressinfoBo bo) {
        try {
            if (bo.getCstUsdcAddress() == null) {
                log.error("USDC ATA地址未预计算，无法激活");
                throw new BaseException("USDC ATA地址未预计算");
            }

            // 激活USDC Token账户（在链上创建）
            activateTokenAccount(
                solanaWalletConfig.getFeeWalletPrivateKey(),
                bo.getCstAddress(),
                SolanaCoinType.USDC.getContractAddress(),
                bo.getCstUsdcAddress()
            );
            log.info("USDC Token账户激活成功: {}", bo.getCstUsdcAddress());

        } catch (Exception e) {
            log.error("激活USDC Token账户失败, 地址: {}, ATA: {}, 错误: {}",
                bo.getCstAddress(), bo.getCstUsdcAddress(), e.getMessage(), e);
            throw e; // 重新抛出异常以触发重试机制
        }
    }

    /**
     * USDT创建失败后的恢复方法
     */
    @Recover
    @SuppressWarnings("unused") // Spring Retry框架运行时调用
    public void recoverCreateUsdtTokenAddress(Exception e, MetaSolanaCstaddressinfoBo bo) {
        log.error("USDT Token地址创建在多次尝试后失败，地址: {}, 最终错误: {}", bo.getCstAddress(), e.getMessage());
        // 这里可以实现失败后的补偿逻辑：记录到失败队列、发送告警等
    }

    /**
     * USDC创建失败后的恢复方法
     */
    @Recover
    @SuppressWarnings("unused") // Spring Retry框架运行时调用
    public void recoverCreateUsdcTokenAddress(Exception e, MetaSolanaCstaddressinfoBo bo) {
        log.error("USDC Token地址创建在多次尝试后失败，地址: {}, 最终错误: {}", bo.getCstAddress(), e.getMessage());
        // 这里可以实现失败后的补偿逻辑：记录到失败队列、发送告警等
    }

    /**
     * 激活Token账户（在链上创建ATA账户）
     *
     * @param fundingPrivateKey 资金账户私钥
     * @param ownerAddress      拥有者地址
     * @param tokenMintAddress  Token mint地址
     * @param ataAddress        预计算的ATA地址
     */
    public void activateTokenAccount(String fundingPrivateKey, String ownerAddress, String tokenMintAddress, String ataAddress) {
        try {
            RpcClient client = solanaRpcConfig.getRpcClient();
            // 解析资金账户私钥
            byte[] decode = decodePrivateKey(fundingPrivateKey);
            Account signer = new Account(decode);
            PublicKey sourcePublicKey = new PublicKey(signer.getPublicKeyBase58());

            // 目标币种和账户
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
            PublicKey destinationPublicKey = new PublicKey(ownerAddress);

            // 创建交易
            Transaction transaction = new Transaction();
            TransactionInstruction createTokenInstruction = AssociatedTokenProgram.create(
                sourcePublicKey, destinationPublicKey, tokenMintPublicKey);
            transaction.addInstruction(createTokenInstruction);

            // 发送交易并等待确认
            String signature = client.getApi().sendTransaction(transaction, signer);
            log.info("Token账户激活交易已提交, 签名: {}, ATA: {}", signature, ataAddress);

            // 等待交易确认（使用SignatureStatuses API优化，动态轮询间隔）
            boolean confirmed = waitForConfirmationWithPolling(signature, 30000L); // 30秒超时
            if (!confirmed) {
                throw new BaseException("Token账户激活交易确认超时, 签名: " + signature);
            }

            log.info("Token账户激活成功并已确认, ATA: {}, 签名: {}", ataAddress, signature);

        } catch (RpcException e) {
            log.error("激活Token账户失败, 拥有者: {}, Token: {}, ATA: {}", ownerAddress, tokenMintAddress, ataAddress, e);
            throw new BaseException("激活Token账户失败:" + e.getMessage());
        }
    }

    /**
     * 获取交易信息
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public ConfirmedTransaction getTransactionInfo(String hash) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        try {
            return client.getApi().getTransaction(hash);
        } catch (RpcException e) {
            log.error("获取交易信息失败, 交易哈希: {}", hash, e);
            throw new BaseException("获取交易信息失败:" + e.getMessage());
        }
    }

    /**
     * 发起SPL代币交易（基础方法，供Strategy调用）
     * <p>
     * 注意：Token转账操作不应该简单重试，因为可能导致重复转账。
     * 交易可能已经成功但返回过程中出现问题，简单重试会导致资金多次转移。
     * <p>
     * 此方法供SolanaTransferStrategy调用，消除代码重复
     */
    public String transferToken(String sourcePrivateKey, String destinationAccount, long amount, String tokenMintAddress) {
        try {
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
            RpcClient client = solanaRpcConfig.getRpcClient();
            // 解码拥有者账户私钥
            byte[] decode = decodePrivateKey(sourcePrivateKey);
            Account signer = new Account(decode);
            PublicKey sourcePublicKey = new PublicKey(signer.getPublicKeyBase58());
            PublicKey sourceTokenPublicKey = client.getApi().getTokenAccountsByOwner(sourcePublicKey, tokenMintPublicKey);

            //目标账户
            PublicKey destinationPublicKey = new PublicKey(destinationAccount);

            //目标mint
            PublicKey destinationTokenPublicKey = client.getApi().getTokenAccountsByOwner(destinationPublicKey, tokenMintPublicKey);

            //转账对象
            Transaction transaction = new Transaction();

            // 如果指定了优先级费用，添加ComputeBudgetProgram指令
            transaction.addInstruction(ComputeBudgetProgram.setComputeUnitPrice(500000));

            transaction.addInstruction(TokenProgram.transferChecked(
                sourceTokenPublicKey,
                destinationTokenPublicKey,
                amount,
                (byte) 6,
                signer.getPublicKey(),
                tokenMintPublicKey));
            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("Solana代币转账失败: to={}, token={}, amount={}, error={}",
                destinationAccount, tokenMintAddress, amount, e.getMessage());
            throw new BaseException("发起Token交易失败:" + e.getMessage());
        }
    }

    /**
     * 从私钥获取拥有者地址
     *
     * @param privateKey 私钥
     * @return 拥有者地址
     */
    public String getOwnerAddressFromPrivateKey(String privateKey) {
        try {
            // 解析私钥
            byte[] decode = decodePrivateKey(privateKey);
            Account account = new Account(decode);
            return account.getPublicKeyBase58();
        } catch (Exception e) {
            log.error("从私钥获取拥有者地址失败", e);
            throw new BaseException("从私钥获取拥有者地址失败:" + e.getMessage());
        }
    }

    /**
     * 安全地检查代币账户是否存在
     *
     * @param ownerAddress     拥有者地址
     * @param tokenMintAddress Token Mint地址
     * @return 如果账户存在返回账户地址，否则返回null
     */
    public String findTokenAccountSafely(String ownerAddress, String tokenMintAddress) {
        try {
            RpcClient client = solanaRpcConfig.getRpcClient();
            PublicKey ownerPublicKey = new PublicKey(ownerAddress);
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);

            // 查找Token账户
            PublicKey tokenAccountPublicKey = client.getApi().getTokenAccountsByOwner(ownerPublicKey, tokenMintPublicKey);
            return tokenAccountPublicKey.toString();
        } catch (RpcException e) {
            if (isTokenAccountNotFoundError(e)) {
                log.debug("钱包 {} 没有代币 {} 的关联账户", ownerAddress, tokenMintAddress);
                return null;
            }
            log.error("查找Token账户失败, 拥有者: {}, Token地址: {}", ownerAddress, tokenMintAddress, e);
            return null;
        }
    }

    /**
     * 查找用户的Token账户地址
     *
     * @param ownerPrivateKey  账户拥有者私钥
     * @param tokenMintAddress Token合约地址
     * @return Token账户地址，如果不存在返回null
     */
    public String findTokenAccountAddress(String ownerPrivateKey, String tokenMintAddress) {
        try {
            RpcClient client = solanaRpcConfig.getRpcClient();
            // 解析拥有者私钥
            byte[] decode = decodePrivateKey(ownerPrivateKey);
            Account owner = new Account(decode);

            // 创建公钥对象
            PublicKey ownerPublicKey = owner.getPublicKey();
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);

            // 查找Token账户
            PublicKey tokenAccountPublicKey = client.getApi().getTokenAccountsByOwner(ownerPublicKey, tokenMintPublicKey);
            return tokenAccountPublicKey.toString();
        } catch (RpcException e) {
            if (isTokenAccountNotFoundError(e)) {
                log.debug("钱包 {} 没有代币 {} 的关联账户", maskPrivateKey(ownerPrivateKey), tokenMintAddress);
                return null;
            }
            log.error("查找Token账户失败, 拥有者: {}, Token地址: {}", maskPrivateKey(ownerPrivateKey), tokenMintAddress, e);
            return null;
        }
    }

    /**
     * 关闭Token账户并回收SOL
     *
     * @param ownerPrivateKey     账户拥有者私钥
     * @param tokenAccountAddress Token账户地址
     * @param destinationAddress  接收回收SOL的地址
     * @return 交易签名
     */
    public String closeTokenAccount(String ownerPrivateKey, String tokenAccountAddress, String destinationAddress) {
        try {
            RpcClient client = solanaRpcConfig.getRpcClient();
            // 解析拥有者私钥
            byte[] decode = decodePrivateKey(ownerPrivateKey);
            Account owner = new Account(decode);

            // 创建公钥对象
            PublicKey tokenAccountPublicKey = new PublicKey(tokenAccountAddress);
            PublicKey destinationPublicKey = new PublicKey(destinationAddress);

            // 创建关闭账户指令
            TransactionInstruction closeInstruction = TokenProgram.closeAccount(
                tokenAccountPublicKey,
                destinationPublicKey,
                owner.getPublicKey()
            );

            // 创建交易
            Transaction transaction = new Transaction();
            transaction.addInstruction(closeInstruction);

            // 发送交易
            return client.getApi().sendTransaction(transaction, owner);
        } catch (RpcException e) {
            log.error("关闭Token账户失败, Token账户: {}, 目标地址: {}", tokenAccountAddress, destinationAddress, e);
            throw new BaseException("关闭Token账户失败:" + e.getMessage());
        }
    }

    /**
     * 安全显示私钥（只显示前5位和后5位）
     */
    private String maskPrivateKey(String privateKey) {
        if (privateKey == null || privateKey.length() < 10) {
            return "***";
        }
        return privateKey.substring(0, 5) + "..." + privateKey.substring(privateKey.length() - 5);
    }

    // ==================== 统一异常转换器 ====================

    /**
     * 统一的Solana异常处理器
     *
     * <p>将所有Solana相关的异常统一转换为BlockchainTransferException，</p>
     * <p>避免在业务逻辑中出现大量的try-catch块。</p>
     *
     * @param operation 要执行的操作
     * @param <T>       返回类型
     * @return 操作结果
     * @throws BlockchainTransferException 转换后的统一异常
     */
    public <T> T executeWithSolanaExceptionHandling(java.util.function.Supplier<T> operation) throws BlockchainTransferException {
        try {
            return operation.get();
        } catch (BlockchainTransferException e) {
            // 已经是我们的异常，直接重新抛出
            throw e;
        } catch (Exception e) {
            // 其他异常，根据类型进行智能转换
            return handleGenericSolanaException(e);
        }
    }

    /**
     * 智能处理Solana通用异常
     */
    private <T> T handleGenericSolanaException(Exception e) throws BlockchainTransferException {
        String message = e.getMessage();
        String exceptionType = e.getClass().getSimpleName();

        // BaseException - Solana特定异常
        if (e instanceof BaseException) {
            throw BlockchainTransferException.wrap("SOLANA", "TRANSFER_FAILED", e);
        }

        // RpcException - RPC异常
        if (e instanceof RpcException) {
            throw BlockchainTransferException.wrap("SOLANA", "RPC_ERROR", e);
        }

        // 余额不足
        if (isInsufficientSolanaBalanceError(e)) {
            throw BlockchainTransferException.insufficientBalance("SOLANA", BigDecimal.ZERO);
        }

        // 网络相关错误
        if (isNetworkError(e)) {
            throw BlockchainTransferException.networkTimeout("SOLANA", e);
        }

        // 账户不存在
        if (message != null && message.contains("account not found")) {
            throw new BlockchainTransferException(
                "Solana账户不存在: " + message,
                "SOLANA",
                "ACCOUNT_NOT_FOUND",
                e
            );
        }

        // 配置错误
        if (message != null && (message.contains("config") || message.contains("配置"))) {
            throw BlockchainTransferException.configurationError("SOLANA", "unknown");
        }

        // 默认服务错误
        throw BlockchainTransferException.wrap("SOLANA", "SERVICE_ERROR", e);
    }

    // ==================== 简化的内部转账方法 ====================

    /**
     * Solana原生代币转账 - 内部实现（无异常处理）
     *
     * <p>纯业务逻辑，不包含异常处理，异常由统一转换器处理</p>
     */
    private BlockchainTransferSuccess transferNativeInternal(String sourcePrivateKey, String toAddress, BigDecimal amount) {
        long lamports = amount.multiply(BigDecimal.valueOf(1_000_000_000L)).longValue();
        String fromAddress = getOwnerAddressFromPrivateKey(sourcePrivateKey);

        // 1. 预估交易费用
        SolanaFeeEstimate feeEstimate = estimateSolanaFee();

        // 2. 检查并提供手续费
        boolean feeProvided = false;
        if (shouldUseFeeWallet(fromAddress, lamports, feeEstimate.solNeeded())) {
            feeProvided = provideSolanaFeeWithRetry(fromAddress, feeEstimate.solNeeded());
            if (!feeProvided) {
                throw new RuntimeException("手续费钱包提供SOL失败");
            }
        }

        // 3. 执行转账
        String txHash = transferNative(sourcePrivateKey, toAddress, lamports);

        log.info("Solana原生代币转账成功: txHash={}, feeProvided={}, feeAmount={}",
            txHash, feeProvided, feeProvided ? feeEstimate.solNeeded() : BigDecimal.ZERO);

        return BlockchainTransferSuccess.withFee(
            txHash,
            feeProvided,
            feeProvided ? feeEstimate.solNeeded() : BigDecimal.ZERO,
            "SOLANA"
        );
    }

    /**
     * Solana代币转账 - 内部实现（无异常处理）
     *
     * <p>纯业务逻辑，不包含异常处理，异常由统一转换器处理</p>
     */
    private BlockchainTransferSuccess transferTokenInternal(String sourcePrivateKey, String toAddress,
                                                            BigDecimal amount, String tokenSymbol) {
        String contractAddress = solanaFacade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new RuntimeException("Solana链不支持代币: " + tokenSymbol);
        }

        int decimals = solanaFacade.getContractDecimals(tokenSymbol);
        long rawAmount = amount.multiply(BigDecimal.TEN.pow(decimals)).longValue();
        String fromAddress = getOwnerAddressFromPrivateKey(sourcePrivateKey);

        log.info("Solana代币转账开始: from={}, to={}, token={}, amount={}",
            fromAddress, toAddress, contractAddress, rawAmount);

        // 1. 预估交易费用
        SolanaFeeEstimate feeEstimate = estimateSolanaFee();

        // 2. 检查并提供手续费
        boolean feeProvided = false;
        if (shouldUseFeeWallet(fromAddress, 0L, feeEstimate.solNeeded())) {
            feeProvided = provideSolanaFeeWithRetry(fromAddress, feeEstimate.solNeeded());
            if (!feeProvided) {
                throw new RuntimeException("手续费钱包提供SOL失败");
            }
        }

        // 3. 执行转账
        String txHash = transferToken(sourcePrivateKey, toAddress, rawAmount, contractAddress);

        log.info("Solana代币转账成功: txHash={}, feeProvided={}, feeAmount={}",
            txHash, feeProvided, feeProvided ? feeEstimate.solNeeded() : BigDecimal.ZERO);

        return BlockchainTransferSuccess.withFee(
            txHash,
            feeProvided,
            feeProvided ? feeEstimate.solNeeded() : BigDecimal.ZERO,
            "SOLANA"
        );
    }

    // ==================== 手续费支持的私有方法 ====================

    /**
     * 预估Solana转账手续费（统一版本）
     */
    public SolanaFeeEstimate estimateSolanaFee() {
        // Solana网络中SOL和SPL代币转账的基础费用都是5000 lamports
        long baseFee = 5000L; // 0.000005 SOL

        // 统一添加20%安全边际，避免网络拥堵时费用不足
        long safeFee = baseFee + (baseFee * 20 / 100);

        BigDecimal solNeeded = new BigDecimal(safeFee).divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.UP);

        return new SolanaFeeEstimate(safeFee, solNeeded);
    }

    /**
     * 预估Solana SOL转账手续费
     *
     * @deprecated 使用 {@link #estimateSolanaFee()} 替代，保持向后兼容
     */
    @Deprecated
    public SolanaFeeEstimate estimateSolanaTransferFee() {
        return estimateSolanaFee();
    }

    /**
     * 预估Solana SPL代币转账手续费
     *
     * @deprecated 使用 {@link #estimateSolanaFee()} 替代，保持向后兼容
     */
    @Deprecated
    public SolanaFeeEstimate estimateSolanaTokenTransferFee() {
        return estimateSolanaFee();
    }

    /**
     * 判断是否应该使用手续费钱包
     */
    public boolean shouldUseFeeWallet(String fromAddress, long transferAmount, BigDecimal feeNeeded) {
        try {
            BigInteger userSolBalance = getBalance(fromAddress);
            BigDecimal userSolBalanceDecimal = new BigDecimal(userSolBalance)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.DOWN);

            // 计算总需要的SOL（转账金额 + 手续费）
            BigDecimal transferAmountDecimal = new BigDecimal(transferAmount)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.DOWN);
            BigDecimal totalNeeded = transferAmountDecimal.add(feeNeeded);

            boolean insufficient = userSolBalanceDecimal.compareTo(totalNeeded) < 0;
            if (insufficient) {
                log.info("Solana余额不足: address={}, current={}, needed={}, 启用手续费钱包",
                    fromAddress, userSolBalanceDecimal, totalNeeded);
            }
            return insufficient;
        } catch (Exception e) {
            log.error("检查用户SOL余额失败: {}", e.getMessage());
            return true; // 出错时使用手续费钱包
        }
    }

    /**
     * 为指定地址提供Solana手续费（公共入口）
     * <p>
     * {{ AURA-X: Modify - 添加公共手续费提供方法，供外部调用. Approval: 寸止(ID:1678886400). }}
     *
     * @param userAddress 用户地址
     * @param solNeeded   需要的SOL数量
     * @return 是否成功提供手续费
     */
    public boolean provideSolanaFee(String userAddress, BigDecimal solNeeded) {
        return provideSolanaFeeWithRetry(userAddress, solNeeded);
    }

    /**
     * 为指定地址提供Solana手续费（带验证和重试）
     * 参考EvmHelper的多层重试机制，针对Solana网络特点优化
     *
     * @param userAddress 用户地址
     * @param solNeeded   需要的SOL数量
     * @return 是否成功提供手续费
     */
    private boolean provideSolanaFeeWithRetry(String userAddress, BigDecimal solNeeded) {
        int maxRetries = 3;
        int baseDelay = 2000; // 2秒基础延迟
        long startTime = System.currentTimeMillis();

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            log.info("尝试为地址 {} 提供Solana手续费，第 {}/{} 次，金额: {} SOL",
                userAddress, attempt, maxRetries, solNeeded);

            try {
                boolean success = provideSolanaFeeWithVerification(userAddress, solNeeded);
                if (success) {
                    long duration = System.currentTimeMillis() - startTime;
                    if (attempt > 1) {
                        log.info("Solana手续费提供在第 {} 次尝试后成功，总耗时: {}ms", attempt, duration);
                    }
                    recordSolanaPerformanceMetrics("provideSolanaFee", duration, true, userAddress, solNeeded);
                    return true;
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries) {
                    int delay = baseDelay * attempt; // 递增延迟：2秒、4秒、6秒
                    log.warn("第 {} 次提供Solana手续费失败，{}秒后重试", attempt, delay / 1000);
                    waitSafely(delay);
                }

            } catch (Exception e) {
                logSolanaError("provideSolanaFee", e, userAddress, solNeeded, attempt);

                // 判断是否应该重试
                if (shouldRetrySolanaOperation(e) && attempt < maxRetries) {
                    int delay = baseDelay * attempt;
                    log.info("第 {} 次提供Solana手续费异常，{}秒后重试: {}", attempt, delay / 1000, e.getMessage());
                    waitSafely(delay);
                } else {
                    log.error("第 {} 次提供Solana手续费异常，不可重试或已达最大重试次数: {}", attempt, e.getMessage());
                    break; // 不可重试的错误，直接退出
                }
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.error("Solana手续费提供在 {} 次尝试后仍然失败，总耗时: {}ms", maxRetries, duration);
        recordSolanaPerformanceMetrics("provideSolanaFee", duration, false, userAddress, solNeeded);
        return false;
    }

    /**
     * 为指定地址提供Solana手续费（简化版本）
     *
     * @param userAddress 用户地址
     * @param solNeeded   需要的SOL数量
     * @return 是否成功提供手续费
     */
    private boolean provideSolanaFeeWithVerification(String userAddress, BigDecimal solNeeded) {
        log.info("Solana手续费提供: address={}, 需要={} SOL", userAddress, solNeeded);

        try {
            // 检查手续费钱包是否启用
            if (!solanaFacade.isFeeWalletEnabled()) {
                log.warn("Solana手续费钱包未启用");
                return false;
            }

            String feeWalletAddress = solanaFacade.getFeeWalletAddress();
            String feeWalletPrivateKey = solanaFacade.getFeeWalletPrivateKey();
            if (feeWalletAddress == null || feeWalletPrivateKey == null) {
                log.warn("Solana手续费钱包配置未找到");
                return false;
            }

            // 1. 获取用户当前SOL余额
            BigInteger userCurrentBalanceLamports = getBalance(userAddress);
            BigDecimal userCurrentBalance = new BigDecimal(userCurrentBalanceLamports)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.DOWN);

            // 2. 计算实际需要补给的差额
            BigDecimal actualNeeded = solNeeded.subtract(userCurrentBalance);
            if (actualNeeded.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("用户SOL余额已足够: {} >= {}，无需补给", userCurrentBalance, solNeeded);
                return true; // 用户余额已足够，无需补给
            }

            // 3. 检查手续费钱包余额是否足够
            BigInteger feeWalletBalance = getBalance(feeWalletAddress);
            BigDecimal feeWalletBalanceDecimal = new BigDecimal(feeWalletBalance)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.DOWN);

            if (feeWalletBalanceDecimal.compareTo(actualNeeded) < 0) {
                log.warn("手续费钱包SOL余额不足: {} < {}", feeWalletBalanceDecimal, actualNeeded);
                return false;
            }

            // 4. 从手续费钱包转账差额到用户地址
            long lamportsToTransfer = actualNeeded.multiply(BigDecimal.valueOf(1_000_000_000L)).longValue();
            String txHash = transferNative(feeWalletPrivateKey, userAddress, lamportsToTransfer);
            log.info("Solana手续费补给已提交: 差额={} SOL, txHash={}", actualNeeded, txHash);

            // 5. 验证转账状态
            boolean verified = quickSolanaStatusCheck(txHash);
            if (verified) {
                log.info("成功为地址 {} 补给 {} SOL 手续费", userAddress, actualNeeded);
                return true;
            } else {
                log.warn("Solana手续费转账验证失败: txHash={}", txHash);
                return false;
            }

        } catch (Exception e) {
            logSolanaError("provideSolanaFeeWithVerification", e, userAddress, solNeeded);
            return false;
        }
    }

    /**
     * 使用手续费钱包重试SOL转账
     * <p>
     * {{ AURA-X: Modify - 合并重复的重试方法，减少代码冗余. Approval: 寸止(ID:1678886400). }}
     * 优化内容：
     * 1. 合并retrySolWithFeeWallet和retryTokenWithFeeWallet方法
     * 2. 使用函数式接口减少重复代码
     * 3. 统一错误处理逻辑
     */
    private SolanaTransferResult retryWithFeeWallet(String sourcePrivateKey, SolanaFeeEstimate feeEstimate,
                                                    String transferType, TransferFunction transferFunction) {
        log.info("使用手续费钱包重试{}转账", transferType);

        String fromAddress = getOwnerAddressFromPrivateKey(sourcePrivateKey);
        boolean retryFeeProvided = provideSolanaFeeWithRetry(fromAddress, feeEstimate.solNeeded());
        if (retryFeeProvided) {
            try {
                String txHash = transferFunction.execute();
                return createSuccessResult(feeEstimate, true, txHash);
            } catch (Exception retryException) {
                log.error("使用手续费钱包重试{}转账也失败: {}", transferType, retryException.getMessage());
                return SolanaTransferResult.failure("即使使用手续费钱包转账仍失败: " + retryException.getMessage());
            }
        }

        return SolanaTransferResult.failure("手续费钱包提供SOL失败");
    }

    /**
     * 转账函数式接口，用于统一重试逻辑
     */
    @FunctionalInterface
    private interface TransferFunction {
        String execute() throws Exception;
    }

    /**
     * 创建成功结果
     */
    private SolanaTransferResult createSuccessResult(SolanaFeeEstimate feeEstimate, boolean feeWalletUsed, String txHash) {
        return SolanaTransferResult.success(txHash, feeWalletUsed,
            feeWalletUsed ? feeEstimate.solNeeded() : BigDecimal.ZERO);
    }

    /**
     * 判断是否为余额不足错误
     */
    private boolean isInsufficientFundsError(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("insufficient funds") ||
            lowerMessage.contains("insufficient balance") ||
            lowerMessage.contains("not enough") ||
            lowerMessage.contains("account not found") ||
            lowerMessage.contains("insufficient lamports");
    }

    /**
     * 检查手续费钱包余额
     */
    public BigDecimal checkFeeWalletBalance() {
        log.debug("检查Solana手续费钱包余额");

        if (!solanaFacade.isFeeWalletEnabled()) {
            log.warn("Solana手续费钱包未启用");
            return BigDecimal.ZERO;
        }

        try {
            String feeWalletAddress = solanaFacade.getFeeWalletAddress();
            BigInteger balance = getBalance(feeWalletAddress);
            return new BigDecimal(balance).divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("检查Solana手续费钱包余额失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 检查手续费钱包是否启用
     */
    public boolean isFeeWalletEnabled() {
        return solanaFacade.isFeeWalletEnabled();
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return solanaFacade.getFeeWalletAddress();
    }

    // ==================== 内部数据类 ====================

    /**
     * Solana手续费预估结果
     */
    public record SolanaFeeEstimate(long lamportsNeeded, BigDecimal solNeeded) {
    }

    /**
     * Solana转账结果
     */
    @Getter
    public static class SolanaTransferResult {
        // Getters
        private final boolean success;
        private final String transactionHash;
        private final boolean feeWalletUsed;
        private final BigDecimal feeProvided;
        private final String errorMessage;

        private SolanaTransferResult(boolean success, String transactionHash, boolean feeWalletUsed,
                                     BigDecimal feeProvided, String errorMessage) {
            this.success = success;
            this.transactionHash = transactionHash;
            this.feeWalletUsed = feeWalletUsed;
            this.feeProvided = feeProvided;
            this.errorMessage = errorMessage;
        }

        public static SolanaTransferResult success(String transactionHash, boolean feeWalletUsed, BigDecimal feeProvided) {
            return new SolanaTransferResult(true, transactionHash, feeWalletUsed, feeProvided, null);
        }

        public static SolanaTransferResult failure(String errorMessage) {
            return new SolanaTransferResult(false, null, false, BigDecimal.ZERO, errorMessage);
        }

    }

    /**
     * 计算关联Token账户地址（ATA）
     * 根据拥有者地址和Token mint地址计算ATA地址，无需上链
     *
     * @param ownerAddress     拥有者地址
     * @param tokenMintAddress Token mint地址
     * @return ATA地址
     */
    public static String calculateAssociatedTokenAddress(String ownerAddress, String tokenMintAddress) {
        try {
            PublicKey ownerPublicKey = new PublicKey(ownerAddress);
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);

            // 使用AssociatedTokenProgram.create创建指令来获取ATA地址
            TransactionInstruction createInstruction = AssociatedTokenProgram.create(
                ownerPublicKey, // payer (这里用owner作为payer)
                ownerPublicKey, // owner
                tokenMintPublicKey // mint
            );

            // 从指令的keys中获取ATA地址（第二个key就是ATA地址）
            return createInstruction.getKeys().get(1).getPublicKey().toString();

        } catch (Exception e) {
            log.error("计算ATA地址失败, 拥有者: {}, Token: {}", ownerAddress, tokenMintAddress, e);
            throw new BaseException("计算ATA地址失败:" + e.getMessage());
        }
    }


    /**
     * 交易状态枚举
     */
    private enum TransactionStatus {
        CONFIRMED,  // 已确认
        FAILED,     // 失败
        PENDING     // 等待中
    }

    /**
     * Solana错误类型分类（参考EvmHelper的错误处理机制）
     */
    private enum SolanaErrorType {
        NETWORK_ERROR,      // 网络错误，可重试
        RPC_ERROR,          // RPC错误，可重试
        BLOCKHASH_ERROR,    // 区块哈希错误，可重试
        SLOT_ERROR,         // Slot相关错误，可重试
        RATE_LIMIT_ERROR,   // 请求频率限制，可重试
        INSUFFICIENT_FUNDS, // 余额不足，不可重试
        ACCOUNT_ERROR,      // 账户错误，不可重试
        SIGNATURE_ERROR,    // 签名错误，不可重试
        PROGRAM_ERROR,      // 程序错误，不可重试
        UNKNOWN_ERROR       // 未知错误，默认重试
    }

    /**
     * Solana确认级别（基于Solana的commitment levels）
     */
    @Getter
    public enum SolanaCommitmentLevel {
        PROCESSED("processed", 0, "最快确认，但可能被回滚"),
        CONFIRMED("confirmed", 1, "平衡速度和安全性，推荐用于大多数场景"),
        FINALIZED("finalized", 32, "最安全确认，不可回滚，用于重要交易");

        private final String value;
        private final int minConfirmations;
        private final String description;

        SolanaCommitmentLevel(String value, int minConfirmations, String description) {
            this.value = value;
            this.minConfirmations = minConfirmations;
            this.description = description;
        }

    }

    /**
     * 记录进度日志
     */
    private void logProgress(int attemptCount, String signature, long startTime) {
        // 前3次每次记录，之后每10次记录一次
        if (attemptCount <= 3 || attemptCount % 10 == 0) {
            long elapsed = System.currentTimeMillis() - startTime;
            log.debug("Solana交易状态查询: signature={}, attempt={}, elapsed={}ms",
                signature, attemptCount, elapsed);
        }
    }

    /**
     * 记录异常日志
     */
    private void logException(int attemptCount, String signature, Exception e) {
        // 前3次每次记录，之后每10次记录一次
        if (attemptCount <= 3 || attemptCount % 10 == 0) {
            log.warn("Solana交易状态查询异常: signature={}, attempt={}, error={}, 继续重试",
                signature, attemptCount, e.getMessage());
        }
    }

    /**
     * 安全等待（使用LockSupport避免忙等待）
     * <p>
     * {{ AURA-X: Modify - 删除未使用的sleepSafely方法，减少代码冗余. Approval: 寸止(ID:1678886400). }}
     * 优化内容：
     * 1. 删除未使用的sleepSafely方法
     * 2. 统一使用waitSafely方法，基于LockSupport实现
     * 3. LockSupport.parkNanos()比Thread.sleep()更轻量，且不会被IDEA标记为忙等待
     */
    private void waitSafely(long millis) {
        if (Thread.currentThread().isInterrupted()) {
            return;
        }

        // 使用LockSupport.parkNanos()，更轻量且避免忙等待警告
        LockSupport.parkNanos(millis * 1_000_000L); // 转换为纳秒

        // 检查中断状态
        if (Thread.currentThread().isInterrupted()) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 快速Solana状态检查（参考EvmHelper的quickStatusCheck）
     * 只检查交易是否上链且成功，不等待确认，适用于手续费验证
     *
     * @param signature 交易签名
     * @return 是否成功
     */
    private boolean quickSolanaStatusCheck(String signature) {
        try {
            long startTime = System.currentTimeMillis();
            int maxAttempts = 5; // 最多检查5次
            int checkInterval = 1000; // 1秒检查间隔

            for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                try {
                    TransactionStatus status = checkTransactionStatusWithSignatureStatuses(signature);

                    if (status == TransactionStatus.CONFIRMED) {
                        long duration = System.currentTimeMillis() - startTime;
                        log.debug("Solana快速状态检查成功: signature={}, attempt={}, duration={}ms",
                            signature, attempt, duration);
                        return true;
                    } else if (status == TransactionStatus.FAILED) {
                        log.debug("Solana快速状态检查失败: signature={}, 交易执行失败", signature);
                        return false;
                    }

                    // 如果还在等待中，继续检查
                    if (attempt < maxAttempts) {
                        waitSafely(checkInterval);
                    }

                } catch (Exception e) {
                    log.debug("Solana快速状态检查异常: signature={}, attempt={}, error={}",
                        signature, attempt, e.getMessage());
                    if (attempt < maxAttempts) {
                        waitSafely(checkInterval);
                    }
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.debug("Solana快速状态检查超时: signature={}, duration={}ms", signature, duration);
            return false;

        } catch (Exception e) {
            log.debug("Solana快速状态检查异常: signature={}, error={}", signature, e.getMessage());
            return false;
        }
    }

    /**
     * 解码私钥（支持Base64和Base58格式）
     * <p>
     * Solana私钥可能以两种格式存储：
     * 1. Base64格式：通常以"=="结尾，长度为88字符
     * 2. Base58格式：不以"=="结尾，长度通常为87-88字符
     * 保持与原始逻辑兼容：优先判断是否以"=="结尾来确定Base64格式
     *
     * @param privateKey 私钥字符串
     * @return 解码后的字节数组
     * @throws BaseException 如果私钥格式无效或解码失败
     */
    public byte[] decodePrivateKey(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            throw new BaseException("私钥不能为空");
        }

        privateKey = privateKey.trim();

        try {
            // 保持原始逻辑：通过"=="结尾判断Base64格式
            if (privateKey.endsWith("==")) {
                return Base64.getDecoder().decode(privateKey);
            } else {
                return Base58.decode(privateKey);
            }
        } catch (IllegalArgumentException e) {
            // 如果第一次解码失败，尝试另一种格式
            try {
                if (privateKey.endsWith("==")) {
                    // 如果是以"=="结尾但Base64解码失败，尝试Base58
                    return Base58.decode(privateKey);
                } else {
                    // 如果不是以"=="结尾但Base58解码失败，尝试Base64
                    return Base64.getDecoder().decode(privateKey);
                }
            } catch (Exception fallbackException) {
                throw new BaseException("私钥格式无效，既不是有效的Base64也不是有效的Base58格式: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new BaseException("私钥解码失败: " + e.getMessage());
        }
    }


    /**
     * 检查交易状态（使用SignatureStatuses API优化版本）
     * <p>
     * 使用getSignatureStatuses替代getTransaction，更高效：
     * 1. 响应更快，数据量更小
     * 2. 提供更详细的确认状态信息
     * 3. 支持批量查询（虽然这里只查询单个）
     */
    private TransactionStatus checkTransactionStatusWithSignatureStatuses(String signature) throws RpcException {
        RpcClient client = solanaRpcConfig.getRpcClient();

        // 使用getSignatureStatuses API查询交易状态
        SignatureStatuses signatureStatuses = client.getApi().getSignatureStatuses(
            Collections.singletonList(signature), true);

        if (signatureStatuses == null || signatureStatuses.getValue() == null ||
            signatureStatuses.getValue().isEmpty()) {
            return TransactionStatus.PENDING;
        }

        var status = signatureStatuses.getValue().get(0);

        // 如果状态为null，说明交易还未被处理
        if (status == null) {
            return TransactionStatus.PENDING;
        }

        // 检查确认状态
        // confirmations为null表示已最终确认，非null表示还在确认中
        if (status.getConfirmations() == null) {
            // 已最终确认，但需要进一步检查是否成功
            // 使用原始方法检查交易是否成功（因为SignatureStatuses可能不包含错误信息）
            return checkTransactionStatus(signature);
        } else {
            log.debug("交易确认中, 签名: {}, 确认数: {}",
                signature, status.getConfirmations());
            return TransactionStatus.PENDING;
        }
    }

    /**
     * 检查交易状态（原版本，保留作为备用）
     * <p>
     * 使用getTransaction API，数据量较大但信息更完整
     */
    private TransactionStatus checkTransactionStatus(String signature) throws RpcException {
        RpcClient client = solanaRpcConfig.getRpcClient();
        ConfirmedTransaction transaction = client.getApi().getTransaction(signature);

        if (transaction == null) {
            return TransactionStatus.PENDING;
        }

        if (transaction.getMeta() == null) {
            return TransactionStatus.PENDING;
        }

        return transaction.getMeta().getErr() == null ?
            TransactionStatus.CONFIRMED : TransactionStatus.FAILED;
    }

    // ==================== 增强的确认机制（基于Solana commitment levels）====================


    /**
     * 等待Solana交易确认（增强版本，支持不同确认级别）
     * 参考EvmHelper的waitForLightConfirmation方法，适配Solana的commitment levels
     *
     * @param signature       交易签名
     * @param commitmentLevel 确认级别
     * @param timeoutSeconds  超时时间（秒）
     * @return 确认结果
     */
    public SolanaTransferResult waitForTransactionConfirmation(String signature,
                                                               SolanaCommitmentLevel commitmentLevel,
                                                               int timeoutSeconds) {
        long startTime = System.currentTimeMillis();
        long timeoutMs = timeoutSeconds * 1000L;

        log.info("开始Solana交易确认: signature={}, commitmentLevel={}, timeout={}秒",
            signature, commitmentLevel.getValue(), timeoutSeconds);

        try {
            return switch (commitmentLevel) {
                case PROCESSED -> waitForProcessedConfirmation(signature, timeoutMs, startTime);
                case CONFIRMED -> waitForConfirmedConfirmation(signature, timeoutMs, startTime);
                case FINALIZED -> waitForFinalizedConfirmation(signature, timeoutMs, startTime);
            };
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logSolanaError("waitForTransactionConfirmation", e, signature, commitmentLevel, timeoutSeconds);
            recordSolanaPerformanceMetrics("waitForTransactionConfirmation", duration, false,
                signature, commitmentLevel);
            return SolanaTransferResult.failure("确认异常: " + e.getMessage());
        }
    }

    /**
     * 等待Processed级别确认（最快，但可能被回滚）
     */
    private SolanaTransferResult waitForProcessedConfirmation(String signature, long timeoutMs, long startTime) {
        int checkInterval = 500; // 500ms检查间隔，更频繁
        int attemptCount = 0;

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            attemptCount++;

            try {
                TransactionStatus status = checkTransactionStatusWithSignatureStatuses(signature);

                if (status == TransactionStatus.CONFIRMED || status == TransactionStatus.FAILED) {
                    long duration = System.currentTimeMillis() - startTime;
                    boolean success = (status == TransactionStatus.CONFIRMED);

                    log.info("Solana交易Processed确认完成: signature={}, success={}, duration={}ms",
                        signature, success, duration);
                    recordSolanaPerformanceMetrics("waitForProcessedConfirmation", duration, success, signature);

                    return success ?
                        SolanaTransferResult.success(signature, false, BigDecimal.ZERO) :
                        SolanaTransferResult.failure("交易执行失败");
                }

                waitSafely(checkInterval);

            } catch (Exception e) {
                if (attemptCount % 10 == 0) { // 每10次记录一次异常
                    log.debug("Processed确认检查异常: signature={}, attempt={}, error={}",
                        signature, attemptCount, e.getMessage());
                }
                waitSafely(checkInterval);
            }

            // 检查线程中断
            if (Thread.currentThread().isInterrupted()) {
                log.warn("Processed确认被中断: signature={}", signature);
                return SolanaTransferResult.failure("确认被中断");
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.warn("Solana交易Processed确认超时: signature={}, timeout={}ms", signature, timeoutMs);
        recordSolanaPerformanceMetrics("waitForProcessedConfirmation", duration, false, signature);
        return SolanaTransferResult.failure("Processed确认超时");
    }

    /**
     * 等待Confirmed级别确认（平衡速度和安全性，推荐）
     */
    private SolanaTransferResult waitForConfirmedConfirmation(String signature, long timeoutMs, long startTime) {
        // 使用现有的waitForConfirmationWithPolling方法，它已经很好地实现了confirmed级别的确认
        boolean confirmed = waitForConfirmationWithPolling(signature, timeoutMs);
        long duration = System.currentTimeMillis() - startTime;

        recordSolanaPerformanceMetrics("waitForConfirmedConfirmation", duration, confirmed, signature);

        return confirmed ?
            SolanaTransferResult.success(signature, false, BigDecimal.ZERO) :
            SolanaTransferResult.failure("Confirmed确认超时或失败");
    }

    /**
     * 等待Finalized级别确认（最安全，不可回滚）
     */
    private SolanaTransferResult waitForFinalizedConfirmation(String signature, long timeoutMs, long startTime) {
        int checkInterval = 2000; // 2秒检查间隔，较慢但更稳定
        int attemptCount = 0;

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            attemptCount++;

            try {
                // 首先检查交易是否成功
                TransactionStatus status = checkTransactionStatusWithSignatureStatuses(signature);

                if (status == TransactionStatus.FAILED) {
                    long duration = System.currentTimeMillis() - startTime;
                    log.info("Solana交易Finalized确认失败: signature={}, duration={}ms", signature, duration);
                    recordSolanaPerformanceMetrics("waitForFinalizedConfirmation", duration, false, signature);
                    return SolanaTransferResult.failure("交易执行失败");
                }

                if (status == TransactionStatus.CONFIRMED) {
                    // 检查是否已经finalized（confirmations为null表示已最终确认）
                    RpcClient client = solanaRpcConfig.getRpcClient();
                    SignatureStatuses signatureStatuses = client.getApi().getSignatureStatuses(
                        Collections.singletonList(signature), true);

                    if (signatureStatuses != null && signatureStatuses.getValue() != null &&
                        !signatureStatuses.getValue().isEmpty()) {
                        var statusInfo = signatureStatuses.getValue().get(0);
                        if (statusInfo != null && statusInfo.getConfirmations() == null) {
                            // 已最终确认
                            long duration = System.currentTimeMillis() - startTime;
                            log.info("Solana交易Finalized确认成功: signature={}, duration={}ms", signature, duration);
                            recordSolanaPerformanceMetrics("waitForFinalizedConfirmation", duration, true, signature);
                            return SolanaTransferResult.success(signature, false, BigDecimal.ZERO);
                        }
                    }
                }

                waitSafely(checkInterval);

            } catch (Exception e) {
                if (attemptCount % 5 == 0) { // 每5次记录一次异常
                    log.debug("Finalized确认检查异常: signature={}, attempt={}, error={}",
                        signature, attemptCount, e.getMessage());
                }
                waitSafely(checkInterval);
            }

            // 检查线程中断
            if (Thread.currentThread().isInterrupted()) {
                log.warn("Finalized确认被中断: signature={}", signature);
                return SolanaTransferResult.failure("确认被中断");
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.warn("Solana交易Finalized确认超时: signature={}, timeout={}ms", signature, timeoutMs);
        recordSolanaPerformanceMetrics("waitForFinalizedConfirmation", duration, false, signature);
        return SolanaTransferResult.failure("Finalized确认超时");
    }

    /**
     * 等待交易确认（使用SignatureStatuses API优化）
     * <p>
     * {{ AURA-X: Modify - 合并重载方法，消除代码重复，统一超时参数管理. Approval: 寸止(ID:1678886400). }}
     * 优化内容：
     * 1. 合并两个重载方法为单一方法，消除50行重复代码
     * 2. 统一超时参数管理，提高代码维护性
     * 3. 保持原有功能不变，确保兼容性
     *
     * <p>使用Solana的getSignatureStatuses API替代getTransaction，更高效且提供更详细的状态信息
     * 采用动态轮询间隔：初期频繁查询，后期降低频率
     * 使用LockSupport.parkNanos()避免忙等待警告
     *
     * @param signature 交易签名
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否确认成功
     */
    private boolean waitForConfirmationWithPolling(String signature, long timeoutMs) {
        final long initialInterval = 500; // 初始500ms
        final long maxInterval = 3000; // 最大3秒
        final double backoffMultiplier = 1.5; // 退避倍数

        log.debug("Solana交易确认开始: signature={}, timeout={}s", signature, timeoutMs / 1000);

        long startTime = System.currentTimeMillis();
        long currentInterval = initialInterval;
        int attemptCount = 0;

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            attemptCount++;

            try {
                TransactionStatus status = checkTransactionStatusWithSignatureStatuses(signature);

                // 记录进度（避免日志过多）
                logProgress(attemptCount, signature, startTime);

                switch (status) {
                    case CONFIRMED:
                        log.info("Solana交易确认成功: signature={}, duration={}ms",
                            signature, System.currentTimeMillis() - startTime);
                        return true;

                    case FAILED:
                        log.error("Solana交易执行失败: signature={}", signature);
                        return false;

                    case PENDING:
                        // 使用LockSupport避免忙等待
                        waitSafely(currentInterval);
                        currentInterval = Math.min((long) (currentInterval * backoffMultiplier), maxInterval);
                        break;
                }

            } catch (Exception e) {
                // 记录异常但继续重试（避免日志过多）
                logException(attemptCount, signature, e);
                waitSafely(currentInterval);
                currentInterval = Math.min((long) (currentInterval * backoffMultiplier), maxInterval);
            }

            // 检查线程中断状态
            if (Thread.currentThread().isInterrupted()) {
                log.warn("等待交易确认被中断, 签名: {}", signature);
                return false;
            }
        }

        log.warn("Solana交易确认超时: signature={}, timeout={}s", signature, timeoutMs / 1000);
        return false;
    }

    // ==================== 错误处理优化（参考EvmHelper机制）====================

    /**
     * 智能分类Solana错误类型
     * 参考EvmHelper的shouldRetryFeeProvision方法，针对Solana特有错误进行分类
     *
     * @param e 异常对象
     * @return Solana错误类型
     */
    private SolanaErrorType classifySolanaError(Exception e) {
        String message = e.getMessage();
        if (message == null) return SolanaErrorType.UNKNOWN_ERROR;

        String lowerMessage = message.toLowerCase();

        // Solana网络相关错误（可重试）
        if (lowerMessage.contains("timeout") ||
            lowerMessage.contains("connection") ||
            lowerMessage.contains("network") ||
            lowerMessage.contains("socket") ||
            lowerMessage.contains("read timed out")) {
            return SolanaErrorType.NETWORK_ERROR;
        }

        // Solana RPC相关错误（可重试）
        if (lowerMessage.contains("rpc") ||
            lowerMessage.contains("json-rpc") ||
            lowerMessage.contains("server error") ||
            lowerMessage.contains("internal error")) {
            return SolanaErrorType.RPC_ERROR;
        }

        // Solana区块哈希相关错误（可重试）
        if (lowerMessage.contains("blockhash") ||
            lowerMessage.contains("recent blockhash") ||
            lowerMessage.contains("invalid blockhash")) {
            return SolanaErrorType.BLOCKHASH_ERROR;
        }

        // Solana Slot相关错误（可重试）
        if (lowerMessage.contains("slot") ||
            lowerMessage.contains("block not available") ||
            lowerMessage.contains("slot was skipped")) {
            return SolanaErrorType.SLOT_ERROR;
        }

        // 请求频率限制（可重试）
        if (lowerMessage.contains("too many requests") ||
            lowerMessage.contains("rate limit") ||
            lowerMessage.contains("429")) {
            return SolanaErrorType.RATE_LIMIT_ERROR;
        }

        // 余额不足（不可重试）
        if (lowerMessage.contains("insufficient funds") ||
            lowerMessage.contains("insufficient lamports") ||
            lowerMessage.contains("account does not have enough sol")) {
            return SolanaErrorType.INSUFFICIENT_FUNDS;
        }

        // 账户相关错误（不可重试）
        if (lowerMessage.contains("account not found") ||
            lowerMessage.contains("invalid account") ||
            lowerMessage.contains("account does not exist") ||
            lowerMessage.contains("invalid account data")) {
            return SolanaErrorType.ACCOUNT_ERROR;
        }

        // 签名相关错误（不可重试）
        if (lowerMessage.contains("invalid signature") ||
            lowerMessage.contains("signature verification failed") ||
            lowerMessage.contains("invalid private key") ||
            lowerMessage.contains("invalid keypair")) {
            return SolanaErrorType.SIGNATURE_ERROR;
        }

        // 程序错误（不可重试）
        if (lowerMessage.contains("program error") ||
            lowerMessage.contains("instruction error") ||
            lowerMessage.contains("custom program error")) {
            return SolanaErrorType.PROGRAM_ERROR;
        }

        return SolanaErrorType.UNKNOWN_ERROR;
    }

    /**
     * 判断是否应该重试Solana操作
     * 参考EvmHelper的shouldRetryFeeProvision方法逻辑
     *
     * @param e 异常对象
     * @return 是否应该重试
     */
    private boolean shouldRetrySolanaOperation(Exception e) {
        SolanaErrorType errorType = classifySolanaError(e);

        return switch (errorType) {
            case NETWORK_ERROR, RPC_ERROR, BLOCKHASH_ERROR, SLOT_ERROR, RATE_LIMIT_ERROR, UNKNOWN_ERROR -> true;
            case INSUFFICIENT_FUNDS, ACCOUNT_ERROR, SIGNATURE_ERROR, PROGRAM_ERROR -> false;
            // 默认重试
        };
    }

    /**
     * 记录Solana错误详情（结构化日志）
     *
     * @param operation 操作名称
     * @param e         异常对象
     * @param context   上下文信息
     */
    private void logSolanaError(String operation, Exception e, Object... context) {
        SolanaErrorType errorType = classifySolanaError(e);
        boolean shouldRetry = shouldRetrySolanaOperation(e);

        log.error("Solana操作错误: operation={}, errorType={}, shouldRetry={}, message={}, context={}, timestamp={}",
            operation, errorType, shouldRetry, e.getMessage(), Arrays.toString(context),
            System.currentTimeMillis());
    }

    /**
     * 记录Solana操作性能指标
     *
     * @param operation 操作名称
     * @param duration  执行时长（毫秒）
     * @param success   是否成功
     * @param context   上下文信息
     */
    private void recordSolanaPerformanceMetrics(String operation, long duration, boolean success, Object... context) {
        log.info("Solana性能指标: operation={}, duration={}ms, success={}, context={}, timestamp={}",
            operation, duration, success, Arrays.toString(context), System.currentTimeMillis());

        // 性能告警阈值检查
        if (duration > 10000) { // 超过10秒
            log.warn("Solana操作性能告警: operation={}, duration={}ms超过阈值", operation, duration);
        }
    }

    // ==================== 异常识别辅助方法 ====================

    /**
     * 判断是否为Solana余额不足错误
     */
    private boolean isInsufficientSolanaBalanceError(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("insufficient") &&
            (lowerMessage.contains("balance") || lowerMessage.contains("funds")) ||
            lowerMessage.contains("余额不足") ||
            lowerMessage.contains("insufficient lamports") ||
            lowerMessage.contains("account does not have enough sol");
    }

    /**
     * 判断是否为网络相关错误
     */
    private boolean isNetworkError(Exception e) {
        String exceptionType = e.getClass().getSimpleName();
        String message = e.getMessage();

        return exceptionType.contains("IOException") ||
            exceptionType.contains("ConnectException") ||
            exceptionType.contains("SocketTimeoutException") ||
            exceptionType.contains("UnknownHostException") ||
            exceptionType.contains("RpcException") ||
            (message != null && (
                message.contains("timeout") ||
                    message.contains("connection") ||
                    message.contains("network") ||
                    message.contains("超时") ||
                    message.contains("网络") ||
                    message.contains("rpc")
            ));
    }

    // ==================== 统一确认接口实现 ====================

    @Override
    public TransactionConfirmationResult confirmTransaction(String txHash, TransactionConfirmationConfig config) {
        try {
            log.debug("Solana统一确认开始: txHash={}, config={}", txHash, config.getDescription());

            // 使用增强的确认机制，默认使用CONFIRMED级别
            SolanaCommitmentLevel commitmentLevel = SolanaCommitmentLevel.CONFIRMED;
            SolanaTransferResult solanaResult = waitForTransactionConfirmation(
                txHash, commitmentLevel, config.getTimeoutSeconds());

            // 转换为统一的确认结果
            if (solanaResult.isSuccess()) {
                log.debug("Solana统一确认成功: txHash={}, commitmentLevel={}",
                    txHash, commitmentLevel.getDescription());
                return TransactionConfirmationResult.success(txHash, commitmentLevel.getMinConfirmations());
            } else {
                log.warn("Solana统一确认失败: txHash={}, error={}",
                    txHash, solanaResult.getErrorMessage());
                return TransactionConfirmationResult.failure(txHash,
                    org.dromara.wallet.wallet.transfer.enums.TransactionStatus.FAILED,
                    "Solana确认失败: " + solanaResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Solana统一确认异常: txHash={}, error={}", txHash, e.getMessage(), e);
            return TransactionConfirmationResult.failure(txHash,
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                "确认过程异常: " + e.getMessage());
        }
    }

    @Override
    public TransactionConfirmationConfig getDefaultConfirmationConfig() {
        return TransactionConfirmationConfig.solanaDefault();
    }

    @Override
    public String getBlockchainType() {
        return "SOLANA";
    }

}
